#!/bin/bash
set -e

echo "🚀 Setting up CertRats Cachet Status Page with Auto-Configuration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker and Docker Compose are available
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed or not in PATH"
    exit 1
fi

# Create necessary directories
print_status "Creating required directories..."
mkdir -p data/cachet/storage data/cachet/bootstrap_cache data/postgres data/redis
mkdir -p logs/nginx logs/monitor logs/cachet
mkdir -p config/nginx config/php config/supervisor config/ssl

# Copy local environment file
print_status "Setting up local environment configuration..."
cp .env.local .env

# Set proper permissions
print_status "Setting directory permissions..."
chmod -R 755 data/
chmod -R 755 logs/
chmod +x init-scripts/setup-cachet.sh
chmod +x entrypoint.sh
chmod +x monitor/certrats_monitor.py

# Stop any existing containers
print_status "Stopping any existing containers..."
docker-compose -f docker-compose.simple.yml down --remove-orphans 2>/dev/null || true

# Build and start services
print_status "Building custom Cachet image..."
docker-compose -f docker-compose.simple.yml build --no-cache

print_status "Starting services..."
docker-compose -f docker-compose.simple.yml up -d

# Wait for services to be ready
print_status "Waiting for services to start..."
sleep 10

# Check service health
print_status "Checking service health..."
for i in {1..30}; do
    if docker-compose -f docker-compose.simple.yml ps | grep -q "Up"; then
        print_success "Services are starting up..."
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Services failed to start within expected time"
        docker-compose -f docker-compose.simple.yml logs
        exit 1
    fi
    sleep 2
done

# Wait for Cachet to be fully ready
print_status "Waiting for Cachet to be fully initialized..."
for i in {1..60}; do
    if curl -s -f http://localhost:8080/api/v1/ping > /dev/null 2>&1; then
        print_success "Cachet is ready!"
        break
    fi
    if [ $i -eq 60 ]; then
        print_warning "Cachet may still be initializing. Check logs if needed."
        break
    fi
    sleep 5
done

# Get API token for monitoring
print_status "Retrieving API token for monitoring..."
API_TOKEN=$(docker-compose -f docker-compose.simple.yml exec -T cachet php artisan tinker --execute="echo App\Models\User::first()->api_key;" 2>/dev/null | tail -n 1 | tr -d '\r\n' || echo "")

if [ -n "$API_TOKEN" ]; then
    print_success "API token retrieved successfully"
    
    # Create monitoring environment file
    cat > monitor/.env << EOF
CACHET_URL=http://localhost:8080
CACHET_TOKEN=$API_TOKEN
CHECK_INTERVAL=60
EOF
    print_success "Monitoring configuration created"
else
    print_warning "Could not retrieve API token. Manual configuration may be needed."
fi

# Display setup summary
echo ""
echo "🎉 CertRats Cachet Status Page Setup Complete!"
echo ""
echo "📊 Access Information:"
echo "   • Status Page: http://localhost:8080"
echo "   • Admin Login: admin / admin123!"
echo "   • API Endpoint: http://localhost:8080/api/v1"
echo ""
echo "🔧 Services Running:"
echo "   • Cachet Application (Port 8080)"
echo "   • PostgreSQL Database (Port 5434)"
echo "   • Redis Cache (Port 6381)"
echo ""
echo "📈 Pre-configured Components:"
echo "   • CertRats Web Application"
echo "   • User Authentication"
echo "   • REST API"
echo "   • AI Certification Planner"
echo "   • PostgreSQL Database"
echo "   • Redis Cache"
echo ""
echo "🔍 To start monitoring:"
echo "   cd monitor && python3 -m pip install -r requirements.txt"
echo "   python3 certrats_monitor.py"
echo ""
echo "📋 Useful Commands:"
echo "   • View logs: docker-compose -f docker-compose.simple.yml logs"
echo "   • Stop services: docker-compose -f docker-compose.simple.yml down"
echo "   • Restart services: docker-compose -f docker-compose.simple.yml restart"
echo ""

# Open browser if possible
if command -v xdg-open &> /dev/null; then
    print_status "Opening status page in browser..."
    xdg-open http://localhost:8080 &
elif command -v open &> /dev/null; then
    print_status "Opening status page in browser..."
    open http://localhost:8080 &
fi

print_success "Setup completed successfully! 🚀"
