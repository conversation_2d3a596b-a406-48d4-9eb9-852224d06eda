#!/bin/bash
set -e

echo "Starting CertRats Cachet Auto-Setup..."

# Wait for database to be ready
echo "Waiting for database connection..."
until php artisan migrate:status > /dev/null 2>&1; do
    echo "Database not ready, waiting..."
    sleep 5
done

echo "Database is ready, proceeding with setup..."

# Run migrations
echo "Running database migrations..."
php artisan migrate --force

# Check if setup is already complete
if php artisan tinker --execute="echo App\Models\User::count();" | grep -q "^0$"; then
    echo "Setting up Cachet for the first time..."
    
    # Create admin user
    echo "Creating admin user..."
    php artisan tinker --execute="
        \$user = new App\Models\User();
        \$user->username = 'admin';
        \$user->email = '<EMAIL>';
        \$user->password = bcrypt('admin123!');
        \$user->firstname = 'CertRats';
        \$user->lastname = 'Admin';
        \$user->level = 1;
        \$user->api_key = str_random(20);
        \$user->active = 1;
        \$user->save();
        echo 'Admin user created successfully';
    "
    
    # Set up basic site settings
    echo "Configuring site settings..."
    php artisan tinker --execute="
        App\Models\Setting::create(['name' => 'app_name', 'value' => 'CertRats Status']);
        App\Models\Setting::create(['name' => 'app_domain', 'value' => 'localhost:8080']);
        App\Models\Setting::create(['name' => 'app_timezone', 'value' => 'UTC']);
        App\Models\Setting::create(['name' => 'app_locale', 'value' => 'en']);
        App\Models\Setting::create(['name' => 'show_support', 'value' => '0']);
        App\Models\Setting::create(['name' => 'app_about', 'value' => 'CertRats certification tracking platform status page']);
        echo 'Site settings configured';
    "
    
    # Create component groups
    echo "Creating component groups..."
    php artisan tinker --execute="
        \$webGroup = App\Models\ComponentGroup::create([
            'name' => 'Web Services',
            'order' => 1,
            'collapsed' => 0
        ]);
        
        \$apiGroup = App\Models\ComponentGroup::create([
            'name' => 'API Services', 
            'order' => 2,
            'collapsed' => 0
        ]);
        
        \$dbGroup = App\Models\ComponentGroup::create([
            'name' => 'Database & Storage',
            'order' => 3, 
            'collapsed' => 0
        ]);
        
        echo 'Component groups created';
    "
    
    # Create components
    echo "Creating service components..."
    php artisan tinker --execute="
        \$webGroup = App\Models\ComponentGroup::where('name', 'Web Services')->first();
        \$apiGroup = App\Models\ComponentGroup::where('name', 'API Services')->first();
        \$dbGroup = App\Models\ComponentGroup::where('name', 'Database & Storage')->first();
        
        App\Models\Component::create([
            'name' => 'CertRats Web Application',
            'description' => 'Main web interface for certification tracking',
            'status' => 1,
            'order' => 1,
            'group_id' => \$webGroup->id,
            'enabled' => true
        ]);
        
        App\Models\Component::create([
            'name' => 'User Authentication',
            'description' => 'User login and authentication system',
            'status' => 1,
            'order' => 2,
            'group_id' => \$webGroup->id,
            'enabled' => true
        ]);
        
        App\Models\Component::create([
            'name' => 'REST API',
            'description' => 'RESTful API for external integrations',
            'status' => 1,
            'order' => 1,
            'group_id' => \$apiGroup->id,
            'enabled' => true
        ]);
        
        App\Models\Component::create([
            'name' => 'AI Certification Planner',
            'description' => 'AI-powered certification path planning service',
            'status' => 1,
            'order' => 2,
            'group_id' => \$apiGroup->id,
            'enabled' => true
        ]);
        
        App\Models\Component::create([
            'name' => 'PostgreSQL Database',
            'description' => 'Primary database for application data',
            'status' => 1,
            'order' => 1,
            'group_id' => \$dbGroup->id,
            'enabled' => true
        ]);
        
        App\Models\Component::create([
            'name' => 'Redis Cache',
            'description' => 'In-memory cache and session storage',
            'status' => 1,
            'order' => 2,
            'group_id' => \$dbGroup->id,
            'enabled' => true
        ]);
        
        echo 'Service components created';
    "
    
    echo "Cachet setup completed successfully!"
else
    echo "Cachet is already set up, skipping initialization..."
fi

echo "CertRats Cachet is ready!"
