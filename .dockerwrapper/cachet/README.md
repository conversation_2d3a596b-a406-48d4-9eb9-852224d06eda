# CertRats Cachet Status Page

A comprehensive status page system for monitoring CertRats infrastructure and services using Cachet, an open-source status page platform.

## 🎯 Overview

This implementation provides:
- **Real-time monitoring** of all CertRats services
- **Public status page** for transparent communication
- **Automated incident management** with API integration
- **Performance metrics** and uptime tracking
- **Notification system** for team alerts and user subscriptions

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Public Users  │    │  CertRats Team  │    │   Monitoring    │
│                 │    │                 │    │    Scripts      │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          │ HTTPS                │ HTTPS                │ API
          │                      │                      │
    ┌─────▼──────────────────────▼──────────────────────▼─────┐
    │                 Nginx Reverse Proxy                     │
    └─────────────────────────┬───────────────────────────────┘
                              │
    ┌─────────────────────────▼───────────────────────────────┐
    │                  Cachet Application                     │
    │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐│
    │  │ Status Page │ │  Dashboard  │ │    API Endpoints    ││
    │  └─────────────┘ └─────────────┘ └─────────────────────┘│
    └─────────────────────────┬───────────────────────────────┘
                              │
    ┌─────────────────────────▼───────────────────────────────┐
    │              Data Layer (PostgreSQL + Redis)            │
    └─────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Access to CertRats infrastructure network
- Domain name for status page (e.g., `status.certrats.com`)

### Installation

1. **Clone and setup**:
   ```bash
   cd .dockerwrapper/cachet
   chmod +x scripts/setup.sh
   ./scripts/setup.sh
   ```

2. **Configure environment**:
   ```bash
   cp config/.env.example .env
   # Edit .env with your specific configuration
   ```

3. **Start services**:
   ```bash
   docker-compose up -d
   ```

4. **Access the dashboard**:
   - Status Page: http://localhost:8080
   - Admin Dashboard: http://localhost:8080/dashboard

## 📁 Directory Structure

```
.dockerwrapper/cachet/
├── docker-compose.yml          # Main service configuration
├── Dockerfile.cachet           # Custom Cachet image
├── .env                        # Environment configuration
├── README.md                   # This file
├── config/                     # Configuration files
│   ├── .env.example           # Environment template
│   ├── nginx/                 # Nginx configurations
│   ├── php/                   # PHP configurations
│   └── supervisor/            # Process management
├── data/                      # Application data
│   ├── components.json        # Service definitions
│   ├── cachet/               # Cachet storage
│   ├── postgres/             # Database data
│   └── redis/                # Cache data
├── logs/                     # Application logs
│   ├── nginx/
│   ├── monitor/
│   └── cachet/
└── scripts/                  # Utility scripts
    ├── setup.sh              # Initial setup
    ├── monitor.py            # Service monitoring
    └── api-integration.py    # CertRats integration
```

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Application
APP_URL=https://status.certrats.com
CACHET_SITE_NAME="CertRats Status"

# Database
DB_HOST=postgres
DB_DATABASE=cachet
DB_USERNAME=cachet_user
DB_PASSWORD=your_secure_password

# Monitoring
CERTRATS_FRONTEND_URL=https://certrats.com
CERTRATS_API_URL=https://api.certrats.com
PROMETHEUS_URL=http://prometheus:9090

# Notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/...
EMAIL_NOTIFICATIONS_ENABLED=true
```

### Service Components

Edit `data/components.json` to configure monitored services:

```json
{
  "components": [
    {
      "name": "CertRats Frontend",
      "endpoint": "https://certrats.com",
      "check_interval": 30,
      "type": "web"
    }
  ]
}
```

## 📊 Monitoring

### Automated Checks

The system automatically monitors:
- **Frontend**: Web application availability and performance
- **API**: Backend service health and response times
- **Database**: Connection status and query performance
- **Cache**: Redis availability and memory usage
- **Infrastructure**: Nginx and container health

### Metrics Collection

- **Response Times**: API and web service latency
- **Uptime**: Service availability percentages
- **Error Rates**: Application and infrastructure errors
- **Resource Usage**: CPU, memory, and disk utilization

### Alert Thresholds

Default thresholds (configurable):
- Response time: >2000ms (warning), >5000ms (critical)
- Error rate: >1% (warning), >5% (critical)
- Uptime: <99.5% (warning), <99% (critical)

## 🔔 Notifications

### Supported Channels

- **Email**: User subscriptions and admin alerts
- **Slack**: Team notifications via webhook
- **Discord**: Community alerts via webhook
- **Webhooks**: Custom integrations

### Notification Types

- Incident created/updated/resolved
- Component status changes
- Scheduled maintenance
- Performance degradation
- Service recovery

## 🛠️ Management

### Daily Operations

```bash
# Check service status
docker-compose ps

# View logs
docker-compose logs cachet
docker-compose logs cachet-monitor

# Restart services
docker-compose restart

# Update configuration
docker-compose down
# Edit configuration files
docker-compose up -d
```

### Backup and Recovery

```bash
# Backup database
docker-compose exec postgres pg_dump -U cachet_user cachet > backup.sql

# Restore database
docker-compose exec -T postgres psql -U cachet_user cachet < backup.sql

# Backup configuration
tar -czf cachet-config-backup.tar.gz config/ data/components.json .env
```

## 🔒 Security

### Access Control

- Admin dashboard protected by authentication
- API access secured with tokens
- Network isolation via Docker networks
- SSL/TLS encryption for all external traffic

### Security Best Practices

- Regular security updates
- Strong password policies
- API token rotation
- Network segmentation
- Audit logging

## 🚨 Troubleshooting

### Common Issues

1. **Service won't start**:
   ```bash
   docker-compose logs cachet
   # Check for configuration errors
   ```

2. **Database connection failed**:
   ```bash
   docker-compose exec postgres pg_isready
   # Verify database is running
   ```

3. **Monitoring not working**:
   ```bash
   docker-compose logs cachet-monitor
   # Check API connectivity
   ```

### Health Checks

```bash
# Test Cachet API
curl http://localhost:8080/api/v1/ping

# Test database connection
docker-compose exec postgres psql -U cachet_user -d cachet -c "SELECT 1;"

# Test Redis connection
docker-compose exec redis redis-cli ping
```

## 📈 Performance Tuning

### Optimization Tips

1. **Database**: Tune PostgreSQL settings for your workload
2. **Cache**: Configure Redis memory limits appropriately
3. **PHP**: Adjust memory limits and OPcache settings
4. **Nginx**: Enable gzip compression and caching
5. **Monitoring**: Adjust check intervals based on requirements

### Scaling Considerations

- Use external PostgreSQL for high availability
- Implement Redis clustering for cache scaling
- Load balance multiple Cachet instances
- Use CDN for static assets

## 🔄 Updates and Maintenance

### Regular Maintenance

- **Weekly**: Review incident reports and performance metrics
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Review and update monitoring configurations
- **Annually**: Disaster recovery testing

### Update Process

```bash
# Backup current installation
./scripts/backup.sh

# Pull latest changes
git pull origin main

# Update containers
docker-compose pull
docker-compose up -d --force-recreate

# Run migrations if needed
docker-compose exec cachet php artisan migrate
```

## 📚 API Documentation

### Component Management

```bash
# List components
curl -H "X-Cachet-Token: YOUR_TOKEN" \
     http://localhost:8080/api/v1/components

# Update component status
curl -X PUT \
     -H "X-Cachet-Token: YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"status": 2}' \
     http://localhost:8080/api/v1/components/1
```

### Incident Management

```bash
# Create incident
curl -X POST \
     -H "X-Cachet-Token: YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"name": "API Degradation", "message": "Investigating slow response times", "status": 1}' \
     http://localhost:8080/api/v1/incidents
```

## 🤝 Contributing

1. Follow the existing code style and conventions
2. Test changes thoroughly in development environment
3. Update documentation for any configuration changes
4. Submit pull requests with clear descriptions

## 📞 Support

For issues and questions:
- Check the troubleshooting section above
- Review Cachet documentation: https://docs.cachethq.io
- Open an issue in the CertRats repository
- Contact the CertRats team

## 📄 License

This implementation is part of the CertRats project and follows the same licensing terms.
