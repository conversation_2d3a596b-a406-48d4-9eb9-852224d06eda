{"components": [{"id": 1, "name": "CertRats Frontend", "description": "Main web application and user interface", "status": 1, "order": 1, "group_id": 1, "enabled": true, "meta": {"type": "web", "endpoint": "https://certrats.com", "check_interval": 30, "timeout": 10, "expected_status_code": 200, "check_ssl": true, "follow_redirects": true, "user_agent": "CertRats-Monitor/1.0"}, "monitoring": {"health_check": {"url": "https://certrats.com/api/health", "method": "GET", "headers": {"Accept": "application/json"}, "expected_response": {"status": "healthy"}}, "performance_metrics": {"response_time_threshold": 2000, "availability_threshold": 99.9}}}, {"id": 2, "name": "CertRats API", "description": "Backend API services and endpoints", "status": 1, "order": 2, "group_id": 1, "enabled": true, "meta": {"type": "api", "endpoint": "https://api.certrats.com/api/v1/health", "check_interval": 30, "timeout": 10, "expected_status_code": 200, "check_ssl": true}, "monitoring": {"health_check": {"url": "https://api.certrats.com/api/v1/health", "method": "GET", "headers": {"Accept": "application/json", "User-Agent": "CertRats-Monitor/1.0"}, "expected_response": {"status": "healthy", "database": "connected", "anthropic_client": "ready"}}, "performance_metrics": {"response_time_threshold": 1000, "availability_threshold": 99.9, "error_rate_threshold": 1}}}, {"id": 3, "name": "AI Career Path Generator", "description": "Claude AI integration for career path recommendations", "status": 1, "order": 3, "group_id": 2, "enabled": true, "meta": {"type": "service", "endpoint": "https://api.certrats.com/api/v1/career-path", "check_interval": 60, "timeout": 30, "expected_status_code": 405, "method": "GET"}, "monitoring": {"health_check": {"url": "https://api.certrats.com/api/v1/career-path", "method": "OPTIONS", "headers": {"Accept": "application/json"}}, "performance_metrics": {"response_time_threshold": 5000, "availability_threshold": 99.5}}}, {"id": 4, "name": "Certification Database", "description": "PostgreSQL database for certifications and user data", "status": 1, "order": 4, "group_id": 3, "enabled": true, "meta": {"type": "database", "endpoint": "internal", "check_interval": 60, "timeout": 5}, "monitoring": {"health_check": {"type": "database", "connection_string": "************************************/certrats", "query": "SELECT 1", "expected_result": 1}, "performance_metrics": {"connection_time_threshold": 1000, "availability_threshold": 99.9, "max_connections_threshold": 80}}}, {"id": 5, "name": "<PERSON><PERSON>", "description": "Redis caching layer for improved performance", "status": 1, "order": 5, "group_id": 3, "enabled": true, "meta": {"type": "cache", "endpoint": "redis://redis:6379", "check_interval": 60, "timeout": 5}, "monitoring": {"health_check": {"type": "redis", "host": "redis", "port": 6379, "command": "PING", "expected_response": "PONG"}, "performance_metrics": {"response_time_threshold": 100, "availability_threshold": 99.9, "memory_usage_threshold": 80}}}, {"id": 6, "name": "User Authentication", "description": "JWT-based authentication and authorization system", "status": 1, "order": 6, "group_id": 2, "enabled": true, "meta": {"type": "service", "endpoint": "https://api.certrats.com/api/v1/auth/health", "check_interval": 30, "timeout": 10, "expected_status_code": 200}, "monitoring": {"health_check": {"url": "https://api.certrats.com/api/v1/auth/health", "method": "GET", "headers": {"Accept": "application/json"}}, "performance_metrics": {"response_time_threshold": 1000, "availability_threshold": 99.9}}}, {"id": 7, "name": "Job Search API", "description": "External job search integration and data processing", "status": 1, "order": 7, "group_id": 2, "enabled": true, "meta": {"type": "service", "endpoint": "https://api.certrats.com/api/v1/jobs/health", "check_interval": 60, "timeout": 15, "expected_status_code": 200}, "monitoring": {"health_check": {"url": "https://api.certrats.com/api/v1/jobs/health", "method": "GET", "headers": {"Accept": "application/json"}}, "performance_metrics": {"response_time_threshold": 3000, "availability_threshold": 99.0}}}, {"id": 8, "name": "<PERSON><PERSON><PERSON> Load Balancer", "description": "Reverse proxy and load balancer", "status": 1, "order": 8, "group_id": 3, "enabled": true, "meta": {"type": "infrastructure", "endpoint": "https://certrats.com/nginx-health", "check_interval": 30, "timeout": 5, "expected_status_code": 200}, "monitoring": {"health_check": {"url": "https://certrats.com/nginx-health", "method": "GET"}, "performance_metrics": {"response_time_threshold": 500, "availability_threshold": 99.9}}}], "component_groups": [{"id": 1, "name": "Core Application", "order": 1, "visible": 1, "collapsed": 0}, {"id": 2, "name": "API Services", "order": 2, "visible": 1, "collapsed": 0}, {"id": 3, "name": "Infrastructure", "order": 3, "visible": 1, "collapsed": 1}], "monitoring_config": {"global_settings": {"default_check_interval": 30, "default_timeout": 10, "max_retries": 3, "retry_delay": 5, "user_agent": "CertRats-Monitor/1.0"}, "thresholds": {"response_time": {"warning": 1000, "critical": 2000}, "availability": {"warning": 99.5, "critical": 99.0}, "error_rate": {"warning": 1, "critical": 5}}, "notifications": {"incident_created": true, "incident_updated": true, "incident_resolved": true, "maintenance_scheduled": true, "component_status_changed": true}, "escalation": {"levels": [{"level": 1, "delay": 0, "channels": ["slack", "email"]}, {"level": 2, "delay": 300, "channels": ["slack", "email", "sms"]}, {"level": 3, "delay": 900, "channels": ["slack", "email", "sms", "phone"]}]}}}