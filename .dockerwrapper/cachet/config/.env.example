# CertRats Cachet Status Page Configuration
# Copy this file to .env and configure with your specific values

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application Environment (production, staging, development)
APP_ENV=production

# Enable debug mode (true/false) - NEVER enable in production
APP_DEBUG=false

# Application URL - Your status page domain
APP_URL=https://status.certrats.com

# Application Key - Generate with: php artisan key:generate
# IMPORTANT: Keep this secret and unique
APP_KEY=base64:CHANGE_THIS_TO_A_SECURE_RANDOM_KEY

# Application Timezone
APP_TIMEZONE=UTC

# Application Locale
APP_LOCALE=en

# Application Fallback Locale
APP_FALLBACK_LOCALE=en

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database Connection Type (pgsql, mysql, sqlite)
DB_CONNECTION=pgsql

# Database Host
DB_HOST=postgres

# Database Port
DB_PORT=5432

# Database Name
DB_DATABASE=cachet

# Database Username
DB_USERNAME=cachet_user

# Database Password
DB_PASSWORD=CHANGE_THIS_TO_A_SECURE_PASSWORD

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================

# Cache Driver (redis, file, array)
CACHE_DRIVER=redis

# Session Driver (redis, file, cookie, database)
SESSION_DRIVER=redis

# Queue Driver (redis, database, sync)
QUEUE_DRIVER=redis

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DATABASE=1
REDIS_PASSWORD=

# =============================================================================
# MAIL CONFIGURATION
# =============================================================================

# Mail Driver (smtp, sendmail, mailgun, ses, log)
MAIL_DRIVER=smtp

# SMTP Configuration
MAIL_HOST=smtp.mailgun.org
MAIL_PORT=587
MAIL_USERNAME=your_mailgun_username
MAIL_PASSWORD=your_mailgun_password
MAIL_ENCRYPTION=tls

# Mail From Address and Name
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="CertRats Status"

# =============================================================================
# CACHET SPECIFIC CONFIGURATION
# =============================================================================

# Status Page Name
CACHET_SITE_NAME="CertRats Status"

# Status Page Domain
CACHET_SITE_DOMAIN=status.certrats.com

# Status Page Timezone
CACHET_SITE_TIMEZONE=UTC

# Status Page Locale
CACHET_SITE_LOCALE=en

# Show Support Link (true/false)
CACHET_SHOW_SUPPORT=false

# Show Powered By Cachet (true/false)
CACHET_SHOW_POWERED_BY=false

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Trusted Proxies (use * for all, or specific IPs)
TRUSTED_PROXIES=*

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://certrats.com,https://www.certrats.com

# Session Configuration
SESSION_LIFETIME=120
SESSION_ENCRYPT=true
SESSION_COOKIE_NAME=cachet_session
SESSION_COOKIE_DOMAIN=.certrats.com
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTP_ONLY=true

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log Channel (single, daily, syslog, errorlog)
LOG_CHANNEL=daily

# Log Level (emergency, alert, critical, error, warning, notice, info, debug)
LOG_LEVEL=info

# Log Max Files (for daily driver)
LOG_MAX_FILES=14

# =============================================================================
# MONITORING INTEGRATION
# =============================================================================

# Cachet API Token for monitoring scripts
CACHET_API_TOKEN=GENERATE_A_SECURE_API_TOKEN

# CertRats Service URLs for monitoring
CERTRATS_FRONTEND_URL=https://certrats.com
CERTRATS_API_URL=https://api.certrats.com
CERTRATS_BACKEND_URL=http://backend:8000

# Prometheus Integration
PROMETHEUS_URL=http://prometheus:9090
PROMETHEUS_ENABLED=true

# Monitoring Configuration
CHECK_INTERVAL=30
ALERT_THRESHOLD_RESPONSE_TIME=2000
ALERT_THRESHOLD_ERROR_RATE=5
ENABLE_AUTO_INCIDENTS=true

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================

# Slack Integration
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SLACK_CHANNEL=#alerts
SLACK_USERNAME=CertRats Status
SLACK_ENABLED=false

# Discord Integration
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK
DISCORD_ENABLED=false

# Email Notifications
EMAIL_NOTIFICATIONS_ENABLED=true
ADMIN_EMAIL=<EMAIL>

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Google Analytics (optional)
GOOGLE_ANALYTICS_ID=

# Sentry Error Tracking (optional)
SENTRY_DSN=

# =============================================================================
# DOCKER SPECIFIC CONFIGURATION
# =============================================================================

# Port Configuration
CACHET_PORT=8080
CACHET_DB_PORT=5433
CACHET_REDIS_PORT=6380
CACHET_HTTP_PORT=8081
CACHET_HTTPS_PORT=8443

# Volume Paths
CACHET_DATA_PATH=./data
CACHET_LOGS_PATH=./logs
CACHET_CONFIG_PATH=./config

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=certrats-backups
BACKUP_S3_REGION=us-east-1

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# PHP Configuration
PHP_MEMORY_LIMIT=256M
PHP_MAX_EXECUTION_TIME=300
PHP_UPLOAD_MAX_FILESIZE=10M
PHP_POST_MAX_SIZE=10M

# OPcache Configuration
OPCACHE_ENABLE=1
OPCACHE_MEMORY_CONSUMPTION=128
OPCACHE_MAX_ACCELERATED_FILES=4000

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================

# SSL Configuration
SSL_ENABLED=true
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem
SSL_DHPARAM_PATH=/etc/nginx/ssl/dhparam.pem

# Let's Encrypt Configuration
LETSENCRYPT_EMAIL=<EMAIL>
LETSENCRYPT_STAGING=false

# =============================================================================
# DEVELOPMENT CONFIGURATION (Development/Staging Only)
# =============================================================================

# Development Settings (DO NOT USE IN PRODUCTION)
# APP_DEBUG=true
# LOG_LEVEL=debug
# XDEBUG_ENABLED=true

# Testing Database (for development)
# TEST_DB_CONNECTION=sqlite
# TEST_DB_DATABASE=:memory:
