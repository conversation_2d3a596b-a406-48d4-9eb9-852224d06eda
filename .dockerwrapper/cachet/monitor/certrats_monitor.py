#!/usr/bin/env python3
"""
CertRats Service Monitor for Cachet Status Page
Automatically monitors services and updates Cachet component statuses
"""

import requests
import time
import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CachetMonitor:
    def __init__(self):
        self.cachet_url = os.getenv('CACHET_URL', 'http://localhost:8080')
        self.cachet_token = os.getenv('CACHET_TOKEN', '')
        self.check_interval = int(os.getenv('CHECK_INTERVAL', '60'))  # seconds
        
        # Service endpoints to monitor
        self.services = {
            'CertRats Web Application': {
                'url': 'http://localhost:8000',
                'method': 'GET',
                'expected_status': 200,
                'timeout': 10
            },
            'REST API': {
                'url': 'http://localhost:8000/api/health',
                'method': 'GET', 
                'expected_status': 200,
                'timeout': 10
            },
            'PostgreSQL Database': {
                'url': 'postgresql://localhost:5434/certrats',
                'type': 'database',
                'timeout': 5
            },
            'Redis Cache': {
                'url': 'redis://localhost:6381',
                'type': 'redis',
                'timeout': 5
            }
        }
        
        # Cachet status codes
        self.status_codes = {
            'operational': 1,
            'performance_issues': 2,
            'partial_outage': 3,
            'major_outage': 4
        }
    
    def get_cachet_headers(self) -> Dict[str, str]:
        """Get headers for Cachet API requests"""
        return {
            'Content-Type': 'application/json',
            'X-Cachet-Token': self.cachet_token
        }
    
    def get_components(self) -> List[Dict]:
        """Fetch all components from Cachet"""
        try:
            response = requests.get(
                f"{self.cachet_url}/api/v1/components",
                headers=self.get_cachet_headers(),
                timeout=10
            )
            response.raise_for_status()
            return response.json().get('data', [])
        except Exception as e:
            logger.error(f"Failed to fetch components: {e}")
            return []
    
    def update_component_status(self, component_id: int, status: int, message: str = "") -> bool:
        """Update component status in Cachet"""
        try:
            data = {
                'status': status
            }
            if message:
                data['description'] = message
                
            response = requests.put(
                f"{self.cachet_url}/api/v1/components/{component_id}",
                headers=self.get_cachet_headers(),
                json=data,
                timeout=10
            )
            response.raise_for_status()
            return True
        except Exception as e:
            logger.error(f"Failed to update component {component_id}: {e}")
            return False
    
    def check_http_service(self, config: Dict) -> bool:
        """Check HTTP/HTTPS service health"""
        try:
            response = requests.request(
                method=config['method'],
                url=config['url'],
                timeout=config['timeout']
            )
            return response.status_code == config['expected_status']
        except Exception as e:
            logger.warning(f"HTTP check failed for {config['url']}: {e}")
            return False
    
    def check_database_service(self, config: Dict) -> bool:
        """Check database connectivity"""
        try:
            import psycopg2
            from urllib.parse import urlparse
            
            parsed = urlparse(config['url'])
            conn = psycopg2.connect(
                host=parsed.hostname,
                port=parsed.port,
                database=parsed.path[1:],  # Remove leading slash
                user=parsed.username,
                password=parsed.password,
                connect_timeout=config['timeout']
            )
            conn.close()
            return True
        except Exception as e:
            logger.warning(f"Database check failed: {e}")
            return False
    
    def check_redis_service(self, config: Dict) -> bool:
        """Check Redis connectivity"""
        try:
            import redis
            from urllib.parse import urlparse
            
            parsed = urlparse(config['url'])
            r = redis.Redis(
                host=parsed.hostname,
                port=parsed.port,
                socket_timeout=config['timeout']
            )
            r.ping()
            return True
        except Exception as e:
            logger.warning(f"Redis check failed: {e}")
            return False
    
    def check_service_health(self, service_name: str, config: Dict) -> bool:
        """Check health of a specific service"""
        service_type = config.get('type', 'http')
        
        if service_type == 'database':
            return self.check_database_service(config)
        elif service_type == 'redis':
            return self.check_redis_service(config)
        else:
            return self.check_http_service(config)
    
    def monitor_services(self):
        """Main monitoring loop"""
        logger.info("Starting CertRats service monitoring...")
        
        while True:
            try:
                # Get current components
                components = self.get_components()
                component_map = {comp['name']: comp for comp in components}
                
                # Check each service
                for service_name, config in self.services.items():
                    if service_name not in component_map:
                        logger.warning(f"Component '{service_name}' not found in Cachet")
                        continue
                    
                    component = component_map[service_name]
                    is_healthy = self.check_service_health(service_name, config)
                    
                    # Determine new status
                    new_status = self.status_codes['operational'] if is_healthy else self.status_codes['major_outage']
                    current_status = component['status']
                    
                    # Update if status changed
                    if new_status != current_status:
                        status_text = "operational" if is_healthy else "major outage"
                        logger.info(f"Status change for {service_name}: {status_text}")
                        
                        success = self.update_component_status(
                            component['id'],
                            new_status,
                            f"Automatically updated at {datetime.now().isoformat()}"
                        )
                        
                        if success:
                            logger.info(f"Successfully updated {service_name} status")
                        else:
                            logger.error(f"Failed to update {service_name} status")
                    else:
                        logger.debug(f"{service_name}: {'healthy' if is_healthy else 'unhealthy'} (no change)")
                
                # Wait before next check
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                logger.info("Monitoring stopped by user")
                break
            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                time.sleep(30)  # Wait before retrying

if __name__ == "__main__":
    monitor = CachetMonitor()
    monitor.monitor_services()
