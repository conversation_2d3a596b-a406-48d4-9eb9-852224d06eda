#!/bin/bash
set -e

echo "Starting CertRats Cachet with auto-configuration..."

# Wait for database to be available
echo "Waiting for database connection..."
until nc -z postgres 5432; do
    echo "Database not ready, waiting..."
    sleep 2
done

echo "Database is available, waiting a bit more for full initialization..."
sleep 5

# Run the setup script
echo "Running Cachet setup..."
/usr/local/bin/setup-cachet.sh

# Start the main process
echo "Starting PHP-FPM..."
exec "$@"
