#!/usr/bin/env python3
"""
CertRats Cachet Monitoring Script

This script monitors CertRats services and updates Cachet status page
with real-time health information and incident management.
"""

import os
import sys
import json
import time
import logging
import requests
import asyncio
import aiohttp
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/logs/monitor.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ComponentStatus(Enum):
    """Cachet component status codes"""
    OPERATIONAL = 1
    PERFORMANCE_ISSUES = 2
    PARTIAL_OUTAGE = 3
    MAJOR_OUTAGE = 4

class IncidentStatus(Enum):
    """Cachet incident status codes"""
    INVESTIGATING = 1
    IDENTIFIED = 2
    WATCHING = 3
    FIXED = 4

@dataclass
class HealthCheckResult:
    """Result of a health check"""
    component_id: int
    component_name: str
    status: ComponentStatus
    response_time: Optional[float]
    error_message: Optional[str]
    timestamp: datetime

class CachetAPI:
    """Cachet API client for status updates"""
    
    def __init__(self, base_url: str, api_token: str):
        self.base_url = base_url.rstrip('/')
        self.api_token = api_token
        self.session = requests.Session()
        self.session.headers.update({
            'X-Cachet-Token': api_token,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def update_component_status(self, component_id: int, status: ComponentStatus, message: str = "") -> bool:
        """Update component status in Cachet"""
        try:
            url = f"{self.base_url}/components/{component_id}"
            data = {
                'status': status.value,
                'meta': {
                    'last_updated': datetime.now(timezone.utc).isoformat(),
                    'message': message
                }
            }
            
            response = self.session.put(url, json=data)
            response.raise_for_status()
            
            logger.info(f"Updated component {component_id} status to {status.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update component {component_id}: {e}")
            return False
    
    def create_incident(self, name: str, message: str, component_id: int, 
                       status: IncidentStatus = IncidentStatus.INVESTIGATING) -> Optional[int]:
        """Create a new incident in Cachet"""
        try:
            url = f"{self.base_url}/incidents"
            data = {
                'name': name,
                'message': message,
                'status': status.value,
                'component_id': component_id,
                'component_status': ComponentStatus.PARTIAL_OUTAGE.value,
                'notify': True
            }
            
            response = self.session.post(url, json=data)
            response.raise_for_status()
            
            incident_id = response.json().get('data', {}).get('id')
            logger.info(f"Created incident {incident_id}: {name}")
            return incident_id
            
        except Exception as e:
            logger.error(f"Failed to create incident: {e}")
            return None
    
    def update_incident(self, incident_id: int, status: IncidentStatus, message: str = "") -> bool:
        """Update an existing incident"""
        try:
            url = f"{self.base_url}/incidents/{incident_id}"
            data = {
                'status': status.value,
                'message': message
            }
            
            response = self.session.put(url, json=data)
            response.raise_for_status()
            
            logger.info(f"Updated incident {incident_id} status to {status.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update incident {incident_id}: {e}")
            return False

class ServiceMonitor:
    """Main service monitoring class"""
    
    def __init__(self, config_path: str = '/app/config/monitoring.yml'):
        self.config = self._load_config()
        self.components = self._load_components()
        self.cachet = CachetAPI(
            base_url=os.getenv('CACHET_API_URL', 'http://cachet:8000/api/v1'),
            api_token=os.getenv('CACHET_API_TOKEN', '')
        )
        self.active_incidents: Dict[int, int] = {}  # component_id -> incident_id
        
    def _load_config(self) -> Dict[str, Any]:
        """Load monitoring configuration"""
        config = {
            'check_interval': int(os.getenv('CHECK_INTERVAL', '30')),
            'timeout': int(os.getenv('TIMEOUT', '10')),
            'max_retries': int(os.getenv('MAX_RETRIES', '3')),
            'response_time_threshold': int(os.getenv('ALERT_THRESHOLD_RESPONSE_TIME', '2000')),
            'error_rate_threshold': float(os.getenv('ALERT_THRESHOLD_ERROR_RATE', '5.0'))
        }
        return config
    
    def _load_components(self) -> List[Dict[str, Any]]:
        """Load component definitions"""
        try:
            with open('/app/data/components.json', 'r') as f:
                data = json.load(f)
                return data.get('components', [])
        except Exception as e:
            logger.error(f"Failed to load components: {e}")
            return []
    
    async def check_http_endpoint(self, session: aiohttp.ClientSession, component: Dict[str, Any]) -> HealthCheckResult:
        """Check HTTP endpoint health"""
        component_id = component['id']
        component_name = component['name']
        endpoint = component['meta']['endpoint']
        timeout = component['meta'].get('timeout', self.config['timeout'])
        
        start_time = time.time()
        
        try:
            async with session.get(endpoint, timeout=timeout) as response:
                response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
                
                if response.status == 200:
                    if response_time > self.config['response_time_threshold']:
                        status = ComponentStatus.PERFORMANCE_ISSUES
                        error_msg = f"High response time: {response_time:.0f}ms"
                    else:
                        status = ComponentStatus.OPERATIONAL
                        error_msg = None
                else:
                    status = ComponentStatus.PARTIAL_OUTAGE
                    error_msg = f"HTTP {response.status}"
                
                return HealthCheckResult(
                    component_id=component_id,
                    component_name=component_name,
                    status=status,
                    response_time=response_time,
                    error_message=error_msg,
                    timestamp=datetime.now(timezone.utc)
                )
                
        except asyncio.TimeoutError:
            return HealthCheckResult(
                component_id=component_id,
                component_name=component_name,
                status=ComponentStatus.MAJOR_OUTAGE,
                response_time=None,
                error_message="Request timeout",
                timestamp=datetime.now(timezone.utc)
            )
        except Exception as e:
            return HealthCheckResult(
                component_id=component_id,
                component_name=component_name,
                status=ComponentStatus.MAJOR_OUTAGE,
                response_time=None,
                error_message=str(e),
                timestamp=datetime.now(timezone.utc)
            )
    
    async def check_all_components(self) -> List[HealthCheckResult]:
        """Check all configured components"""
        results = []
        
        async with aiohttp.ClientSession() as session:
            tasks = []
            
            for component in self.components:
                if not component.get('enabled', True):
                    continue
                
                component_type = component['meta'].get('type', 'web')
                
                if component_type in ['web', 'api', 'service']:
                    task = self.check_http_endpoint(session, component)
                    tasks.append(task)
            
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Filter out exceptions and log them
                valid_results = []
                for result in results:
                    if isinstance(result, Exception):
                        logger.error(f"Health check failed: {result}")
                    else:
                        valid_results.append(result)
                
                results = valid_results
        
        return results
    
    def process_health_results(self, results: List[HealthCheckResult]):
        """Process health check results and update Cachet"""
        for result in results:
            # Update component status
            self.cachet.update_component_status(
                component_id=result.component_id,
                status=result.status,
                message=result.error_message or ""
            )
            
            # Handle incident management
            if result.status in [ComponentStatus.PARTIAL_OUTAGE, ComponentStatus.MAJOR_OUTAGE]:
                self._handle_incident_creation(result)
            elif result.status == ComponentStatus.OPERATIONAL:
                self._handle_incident_resolution(result)
            
            # Log result
            logger.info(
                f"Component {result.component_name}: {result.status.name} "
                f"(Response: {result.response_time:.0f}ms)" if result.response_time else ""
            )
    
    def _handle_incident_creation(self, result: HealthCheckResult):
        """Create incident if component is down"""
        if result.component_id not in self.active_incidents:
            incident_name = f"{result.component_name} - {result.status.name.replace('_', ' ').title()}"
            incident_message = f"We are investigating issues with {result.component_name}."
            
            if result.error_message:
                incident_message += f" Error: {result.error_message}"
            
            incident_id = self.cachet.create_incident(
                name=incident_name,
                message=incident_message,
                component_id=result.component_id,
                status=IncidentStatus.INVESTIGATING
            )
            
            if incident_id:
                self.active_incidents[result.component_id] = incident_id
    
    def _handle_incident_resolution(self, result: HealthCheckResult):
        """Resolve incident if component is back online"""
        if result.component_id in self.active_incidents:
            incident_id = self.active_incidents[result.component_id]
            
            self.cachet.update_incident(
                incident_id=incident_id,
                status=IncidentStatus.FIXED,
                message=f"{result.component_name} is now operational."
            )
            
            del self.active_incidents[result.component_id]
    
    async def run_monitoring_loop(self):
        """Main monitoring loop"""
        logger.info("Starting CertRats service monitoring...")
        
        while True:
            try:
                logger.info("Running health checks...")
                results = await self.check_all_components()
                self.process_health_results(results)
                
                logger.info(f"Completed health checks. Sleeping for {self.config['check_interval']} seconds...")
                await asyncio.sleep(self.config['check_interval'])
                
            except KeyboardInterrupt:
                logger.info("Monitoring stopped by user")
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(10)  # Short sleep before retrying

def main():
    """Main entry point"""
    try:
        monitor = ServiceMonitor()
        asyncio.run(monitor.run_monitoring_loop())
    except Exception as e:
        logger.error(f"Failed to start monitoring: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
