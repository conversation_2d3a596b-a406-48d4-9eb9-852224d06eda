#!/bin/bash

# CertRats Cachet Status Page Setup Script
# This script initializes the Cachet status page system for CertRats monitoring

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ROOT_DIR="$(dirname "$(dirname "$PROJECT_DIR")")"

# Default values
ENVIRONMENT=${ENVIRONMENT:-production}
SKIP_BUILD=${SKIP_BUILD:-false}
FORCE_RECREATE=${FORCE_RECREATE:-false}

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is available
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    
    log_success "All dependencies are available"
}

create_directories() {
    log_info "Creating necessary directories..."
    
    # Create data directories
    mkdir -p "$PROJECT_DIR/data/cachet/storage"
    mkdir -p "$PROJECT_DIR/data/cachet/bootstrap_cache"
    mkdir -p "$PROJECT_DIR/data/postgres"
    mkdir -p "$PROJECT_DIR/data/redis"
    
    # Create log directories
    mkdir -p "$PROJECT_DIR/logs/nginx"
    mkdir -p "$PROJECT_DIR/logs/monitor"
    mkdir -p "$PROJECT_DIR/logs/cachet"
    
    # Create config directories
    mkdir -p "$PROJECT_DIR/config/nginx"
    mkdir -p "$PROJECT_DIR/config/php"
    mkdir -p "$PROJECT_DIR/config/supervisor"
    mkdir -p "$PROJECT_DIR/config/ssl"
    
    # Set proper permissions
    chmod -R 755 "$PROJECT_DIR/data"
    chmod -R 755 "$PROJECT_DIR/logs"
    
    log_success "Directories created successfully"
}

setup_environment() {
    log_info "Setting up environment configuration..."
    
    if [[ ! -f "$PROJECT_DIR/.env" ]]; then
        if [[ -f "$PROJECT_DIR/config/.env.example" ]]; then
            cp "$PROJECT_DIR/config/.env.example" "$PROJECT_DIR/.env"
            log_success "Environment file created from template"
            log_warning "Please edit .env file with your specific configuration"
        else
            log_error "Environment template not found"
            exit 1
        fi
    else
        log_info "Environment file already exists"
    fi
}

generate_secrets() {
    log_info "Generating secure secrets..."
    
    # Generate APP_KEY if not set
    if ! grep -q "APP_KEY=base64:" "$PROJECT_DIR/.env" 2>/dev/null; then
        APP_KEY=$(openssl rand -base64 32)
        sed -i "s/APP_KEY=.*/APP_KEY=base64:$APP_KEY/" "$PROJECT_DIR/.env"
        log_success "Generated APP_KEY"
    fi
    
    # Generate API token if not set
    if ! grep -q "CACHET_API_TOKEN=" "$PROJECT_DIR/.env" 2>/dev/null || grep -q "CACHET_API_TOKEN=GENERATE" "$PROJECT_DIR/.env" 2>/dev/null; then
        API_TOKEN=$(openssl rand -hex 32)
        sed -i "s/CACHET_API_TOKEN=.*/CACHET_API_TOKEN=$API_TOKEN/" "$PROJECT_DIR/.env"
        log_success "Generated CACHET_API_TOKEN"
    fi
    
    # Generate database password if not set
    if ! grep -q "DB_PASSWORD=" "$PROJECT_DIR/.env" 2>/dev/null || grep -q "DB_PASSWORD=CHANGE" "$PROJECT_DIR/.env" 2>/dev/null; then
        DB_PASSWORD=$(openssl rand -base64 24)
        sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASSWORD/" "$PROJECT_DIR/.env"
        sed -i "s/CACHET_DB_PASSWORD=.*/CACHET_DB_PASSWORD=$DB_PASSWORD/" "$PROJECT_DIR/.env"
        log_success "Generated database password"
    fi
}

create_network() {
    log_info "Creating Docker network..."
    
    # Check if certrats network exists
    if ! docker network ls | grep -q "certrats_certrats-network"; then
        log_warning "CertRats network not found. Creating isolated network..."
        docker network create cachet-network || true
    else
        log_success "CertRats network found"
    fi
}

build_images() {
    if [[ "$SKIP_BUILD" == "false" ]]; then
        log_info "Building Docker images..."
        
        cd "$PROJECT_DIR"
        
        if [[ "$FORCE_RECREATE" == "true" ]]; then
            docker-compose build --no-cache
        else
            docker-compose build
        fi
        
        log_success "Docker images built successfully"
    else
        log_info "Skipping image build"
    fi
}

start_services() {
    log_info "Starting Cachet services..."
    
    cd "$PROJECT_DIR"
    
    if [[ "$FORCE_RECREATE" == "true" ]]; then
        docker-compose down -v
        docker-compose up -d --force-recreate
    else
        docker-compose up -d
    fi
    
    log_success "Services started successfully"
}

wait_for_services() {
    log_info "Waiting for services to be ready..."
    
    # Wait for database
    log_info "Waiting for database..."
    timeout=60
    while ! docker-compose exec -T postgres pg_isready -U cachet_user -d cachet &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            log_error "Database failed to start within timeout"
            exit 1
        fi
    done
    log_success "Database is ready"
    
    # Wait for Redis
    log_info "Waiting for Redis..."
    timeout=30
    while ! docker-compose exec -T redis redis-cli ping &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            log_error "Redis failed to start within timeout"
            exit 1
        fi
    done
    log_success "Redis is ready"
    
    # Wait for Cachet
    log_info "Waiting for Cachet..."
    timeout=120
    while ! curl -f http://localhost:8080/api/v1/ping &>/dev/null; do
        sleep 5
        timeout=$((timeout - 5))
        if [[ $timeout -le 0 ]]; then
            log_error "Cachet failed to start within timeout"
            exit 1
        fi
    done
    log_success "Cachet is ready"
}

run_migrations() {
    log_info "Running database migrations..."
    
    cd "$PROJECT_DIR"
    docker-compose exec cachet php artisan migrate --force
    
    log_success "Database migrations completed"
}

setup_components() {
    log_info "Setting up monitoring components..."
    
    # This would typically involve API calls to create components
    # For now, we'll just log that this step is needed
    log_warning "Component setup requires manual configuration via Cachet dashboard"
    log_info "Visit http://localhost:8080/dashboard to configure components"
}

show_status() {
    log_info "Checking service status..."
    
    cd "$PROJECT_DIR"
    docker-compose ps
    
    echo ""
    log_success "Setup completed successfully!"
    echo ""
    echo "Access URLs:"
    echo "  - Status Page: http://localhost:8080"
    echo "  - Admin Dashboard: http://localhost:8080/dashboard"
    echo ""
    echo "Next steps:"
    echo "  1. Visit the dashboard to complete initial setup"
    echo "  2. Configure components using data/components.json as reference"
    echo "  3. Set up monitoring scripts"
    echo "  4. Configure domain and SSL certificates"
    echo ""
}

cleanup() {
    if [[ $? -ne 0 ]]; then
        log_error "Setup failed. Cleaning up..."
        cd "$PROJECT_DIR"
        docker-compose down
    fi
}

# Main execution
main() {
    log_info "Starting CertRats Cachet Status Page setup..."
    
    # Set up cleanup trap
    trap cleanup EXIT
    
    # Run setup steps
    check_dependencies
    create_directories
    setup_environment
    generate_secrets
    create_network
    build_images
    start_services
    wait_for_services
    run_migrations
    setup_components
    show_status
    
    # Remove cleanup trap on success
    trap - EXIT
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --environment|-e)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --force-recreate)
            FORCE_RECREATE=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -e, --environment ENV    Set environment (production, staging, development)"
            echo "  --skip-build            Skip Docker image building"
            echo "  --force-recreate        Force recreate all containers"
            echo "  -h, --help              Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main
