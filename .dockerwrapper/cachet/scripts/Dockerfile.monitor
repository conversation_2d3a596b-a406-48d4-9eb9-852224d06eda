# CertRats Cachet Monitoring Service Dockerfile
FROM python:3.11-alpine

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    curl \
    bash \
    && rm -rf /var/cache/apk/*

# Create application user
RUN addgroup -g 1000 -S monitor && \
    adduser -u 1000 -S monitor -G monitor

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy monitoring script
COPY monitor.py /app/monitor.py
COPY healthcheck.py /app/healthcheck.py

# Create necessary directories
RUN mkdir -p /app/logs /app/config /app/data \
    && chown -R monitor:monitor /app

# Copy configuration files
COPY ../config/monitoring.yml /app/config/monitoring.yml
COPY ../data/components.json /app/data/components.json

# Set proper permissions
RUN chmod +x /app/monitor.py /app/healthcheck.py \
    && chown -R monitor:monitor /app

# Health check
HEALTHCHECK --interval=60s --timeout=10s --start-period=30s --retries=3 \
    CMD python /app/healthcheck.py

# Expose health check port
EXPOSE 8001

# Switch to non-root user
USER monitor

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Default command
CMD ["python", "/app/monitor.py"]

# Build arguments and labels
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION

LABEL maintainer="CertRats Team <<EMAIL>>" \
      org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="CertRats Cachet Monitor" \
      org.label-schema.description="Service monitoring for CertRats Cachet status page" \
      org.label-schema.url="https://status.certrats.com" \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.vcs-url="https://github.com/forkrul/replit-CertPathFinder" \
      org.label-schema.vendor="CertRats" \
      org.label-schema.version=$VERSION \
      org.label-schema.schema-version="1.0"
