# Cachet Status Page Dockerfile
# Based on Cachet v3.x with PHP 8.3 and optimizations for CertRats
FROM php:8.3-fpm-alpine AS base

# Set working directory
WORKDIR /var/www/html

# Install system dependencies
RUN apk add --no-cache \
    git \
    curl \
    libpng-dev \
    libxml2-dev \
    zip \
    unzip \
    oniguruma-dev \
    postgresql-dev \
    redis \
    nginx \
    supervisor \
    nodejs \
    npm \
    bash \
    && docker-php-ext-install \
    pdo \
    pdo_pgsql \
    mbstring \
    xml \
    gd \
    bcmath \
    opcache

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Install Redis PHP extension
RUN pecl install redis && docker-php-ext-enable redis

# Create application user
RUN addgroup -g 1000 -S www && \
    adduser -u 1000 -S www -G www

# Development stage
FROM base AS development

# Install development dependencies
RUN apk add --no-cache \
    xdebug \
    && docker-php-ext-enable xdebug

# Configure Xdebug
RUN echo "xdebug.mode=debug" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
    && echo "xdebug.start_with_request=yes" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
    && echo "xdebug.client_host=host.docker.internal" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
    && echo "xdebug.client_port=9003" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini

# Production stage
FROM base AS production

# Copy PHP configuration
COPY config/php/php.ini /usr/local/etc/php/php.ini
COPY config/php/opcache.ini /usr/local/etc/php/conf.d/opcache.ini

# Clone Cachet repository
RUN git clone -b 3.x --depth 1 https://github.com/cachethq/cachet.git . \
    && rm -rf .git

# Install PHP dependencies
RUN composer install --no-dev --optimize-autoloader --no-interaction --no-progress

# Update Cachet core (temporary step for v3.x development)
RUN composer update cachethq/core --no-interaction

# Set up application structure
RUN mkdir -p storage/logs storage/framework/cache storage/framework/sessions storage/framework/views \
    && mkdir -p bootstrap/cache \
    && chown -R www:www storage bootstrap/cache \
    && chmod -R 775 storage bootstrap/cache

# Copy application configuration
COPY config/cachet/.env.production .env
COPY config/cachet/config.php config/cachet.php

# Generate application key (will be overridden by environment variable)
RUN php artisan key:generate --no-interaction

# Publish Cachet assets
RUN php artisan vendor:publish --tag=cachet --no-interaction

# Install and build frontend assets
RUN npm install && npm run build

# Copy Nginx configuration
COPY config/nginx/cachet.conf /etc/nginx/http.d/default.conf
COPY config/nginx/nginx.conf /etc/nginx/nginx.conf

# Copy Supervisor configuration
COPY config/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Copy startup scripts
COPY scripts/entrypoint.sh /usr/local/bin/entrypoint.sh
COPY scripts/wait-for-it.sh /usr/local/bin/wait-for-it.sh
RUN chmod +x /usr/local/bin/entrypoint.sh /usr/local/bin/wait-for-it.sh

# Copy health check script
COPY scripts/healthcheck.sh /usr/local/bin/healthcheck.sh
RUN chmod +x /usr/local/bin/healthcheck.sh

# Set proper permissions
RUN chown -R www:www /var/www/html \
    && chmod -R 755 /var/www/html \
    && chmod -R 775 storage bootstrap/cache

# Create necessary directories for Nginx and PHP-FPM
RUN mkdir -p /var/log/nginx /var/log/php-fpm /run/nginx \
    && chown -R www:www /var/log/nginx /var/log/php-fpm /run/nginx

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

# Set user
USER www

# Entry point
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]

# Default command
CMD ["supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]

# Build arguments and labels
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION

LABEL maintainer="CertRats Team <<EMAIL>>" \
      org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="CertRats Cachet Status Page" \
      org.label-schema.description="Cachet status page system for CertRats service monitoring" \
      org.label-schema.url="https://status.certrats.com" \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.vcs-url="https://github.com/forkrul/replit-CertPathFinder" \
      org.label-schema.vendor="CertRats" \
      org.label-schema.version=$VERSION \
      org.label-schema.schema-version="1.0"
