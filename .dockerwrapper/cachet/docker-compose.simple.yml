# Simplified Cachet Docker Compose for Quick Start
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: certrats-cachet-postgres
    environment:
      - POSTGRES_DB=cachet
      - POSTGRES_USER=cachet_user
      - POSTGRES_PASSWORD=cachet_password_123
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - cachet-network
    restart: unless-stopped
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U cachet_user -d cachet" ]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: certrats-cachet-redis
    ports:
      - "6381:6379"
    volumes:
      - redis_data:/data
    networks:
      - cachet-network
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 5

  # Cachet Status Page (using custom image with auto-setup)
  cachet:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: certrats-cachet
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - APP_URL=http://localhost:8080
      - APP_KEY=base64:WfKUhY0wAOyr5hkxoctZUSWPGdXbBUWFvNLCoI7kTU8=

      # Database Configuration
      - DB_DRIVER=pgsql
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_DATABASE=cachet
      - DB_USERNAME=cachet_user
      - DB_PASSWORD=cachet_password_123

      # Cache Configuration
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DATABASE=1

      # Mail Configuration (using log driver for testing)
      - MAIL_DRIVER=log
      - MAIL_FROM_ADDRESS=<EMAIL>
      - MAIL_FROM_NAME=CertRats Status

      # Auto-setup Configuration
      - CACHET_SETUP=true
      - CACHET_SITE_NAME=CertRats Status
      - CACHET_SITE_DOMAIN=localhost:8080
      - CACHET_SITE_TIMEZONE=UTC
      - CACHET_ADMIN_USERNAME=admin
      - CACHET_ADMIN_EMAIL=<EMAIL>
      - CACHET_ADMIN_PASSWORD=admin123!
      - CACHET_ADMIN_FIRSTNAME=CertRats
      - CACHET_ADMIN_LASTNAME=Admin

    ports:
      - "8080:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - cachet-network
    volumes:
      - cachet_storage:/var/www/html/storage
      - ./data/cachet/bootstrap_cache:/var/www/html/bootstrap/cache
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8000/api/v1/ping" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  cachet_storage:
    driver: local

networks:
  cachet-network:
    driver: bridge
