# Cachet Status Page Docker Compose Configuration
# Part of CertRats Service Monitoring Infrastructure
version: '3.8'

services:
  # Cachet Status Page Application
  cachet:
    build:
      context: .
      dockerfile: Dockerfile.cachet
    container_name: certrats-cachet
    environment:
      # Application Configuration
      - APP_ENV=production
      - APP_DEBUG=false
      - APP_KEY=${CACHET_APP_KEY}
      - APP_URL=https://status.certrats.com
      - APP_TIMEZONE=UTC
      
      # Database Configuration
      - DB_CONNECTION=pgsql
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_DATABASE=cachet
      - DB_USERNAME=${CACHET_DB_USER:-cachet_user}
      - DB_PASSWORD=${CACHET_DB_PASSWORD}
      
      # Cache and Session Configuration
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DATABASE=1
      
      # Mail Configuration
      - MAIL_DRIVER=${MAIL_DRIVER:-smtp}
      - MAIL_HOST=${MAIL_HOST}
      - MAIL_PORT=${MAIL_PORT:-587}
      - MAIL_USERNAME=${MAIL_USERNAME}
      - MAIL_PASSWORD=${MAIL_PASSWORD}
      - MAIL_ENCRYPTION=${MAIL_ENCRYPTION:-tls}
      - MAIL_FROM_ADDRESS=${MAIL_FROM_ADDRESS:-<EMAIL>}
      - MAIL_FROM_NAME=${MAIL_FROM_NAME:-CertRats Status}
      
      # Queue Configuration
      - QUEUE_DRIVER=redis
      
      # Security Configuration
      - TRUSTED_PROXIES=*
      - APP_LOG=daily
      - APP_LOG_LEVEL=info
      
    ports:
      - "${CACHET_PORT:-8080}:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - cachet-network
      - certrats-network
    volumes:
      - cachet_storage:/var/www/html/storage
      - cachet_bootstrap_cache:/var/www/html/bootstrap/cache
      - ./config/cachet.conf:/etc/nginx/sites-available/default:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cachet.rule=Host(`status.certrats.com`)"
      - "traefik.http.routers.cachet.tls=true"
      - "traefik.http.routers.cachet.tls.certresolver=letsencrypt"

  # PostgreSQL Database (if not using external)
  postgres:
    image: postgres:15-alpine
    container_name: certrats-cachet-postgres
    environment:
      - POSTGRES_DB=cachet
      - POSTGRES_USER=${CACHET_DB_USER:-cachet_user}
      - POSTGRES_PASSWORD=${CACHET_DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "${CACHET_DB_PORT:-5433}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - cachet-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${CACHET_DB_USER:-cachet_user} -d cachet"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: certrats-cachet-redis
    ports:
      - "${CACHET_REDIS_PORT:-6380}:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - cachet-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server /usr/local/etc/redis/redis.conf

  # Cachet Service Monitor
  cachet-monitor:
    build:
      context: ./scripts
      dockerfile: Dockerfile.monitor
    container_name: certrats-cachet-monitor
    environment:
      # Cachet API Configuration
      - CACHET_API_URL=http://cachet:8000/api/v1
      - CACHET_API_TOKEN=${CACHET_API_TOKEN}
      
      # CertRats Services Configuration
      - CERTRATS_FRONTEND_URL=${CERTRATS_FRONTEND_URL:-https://certrats.com}
      - CERTRATS_API_URL=${CERTRATS_API_URL:-https://api.certrats.com}
      - CERTRATS_BACKEND_URL=${CERTRATS_BACKEND_URL:-http://backend:8000}
      
      # Monitoring Configuration
      - PROMETHEUS_URL=${PROMETHEUS_URL:-http://prometheus:9090}
      - CHECK_INTERVAL=${CHECK_INTERVAL:-30}
      - ALERT_THRESHOLD_RESPONSE_TIME=${ALERT_THRESHOLD_RESPONSE_TIME:-2000}
      - ALERT_THRESHOLD_ERROR_RATE=${ALERT_THRESHOLD_ERROR_RATE:-5}
      
      # Notification Configuration
      - SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL}
      - DISCORD_WEBHOOK_URL=${DISCORD_WEBHOOK_URL}
      - ENABLE_NOTIFICATIONS=${ENABLE_NOTIFICATIONS:-true}
      
    depends_on:
      cachet:
        condition: service_healthy
    networks:
      - cachet-network
      - certrats-network
    volumes:
      - ./config/monitoring.yml:/app/config/monitoring.yml:ro
      - ./data/components.json:/app/data/components.json:ro
      - monitor_logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8001/health')"]
      interval: 60s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (Optional - for SSL termination)
  nginx:
    image: nginx:alpine
    container_name: certrats-cachet-nginx
    ports:
      - "${CACHET_HTTP_PORT:-8081}:80"
      - "${CACHET_HTTPS_PORT:-8443}:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - cachet
    networks:
      - cachet-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  # Cachet Application Data
  cachet_storage:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/cachet/storage
  
  cachet_bootstrap_cache:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/cachet/bootstrap_cache
  
  # Database Data
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/postgres
  
  # Cache Data
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/redis
  
  # Logs
  nginx_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs/nginx
  
  monitor_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs/monitor

networks:
  # Internal Cachet Network
  cachet-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  
  # External CertRats Network (connects to main infrastructure)
  certrats-network:
    external: true
    name: certrats_certrats-network
