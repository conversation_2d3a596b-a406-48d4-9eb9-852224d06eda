FROM cachethq/docker:latest

# Install additional tools needed for setup
USER root
RUN apk add --no-cache curl jq

# Copy initialization scripts
COPY init-scripts/setup-cachet.sh /usr/local/bin/setup-cachet.sh
RUN chmod +x /usr/local/bin/setup-cachet.sh

# Copy custom entrypoint
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# Switch back to www-data user
USER www-data

# Use custom entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["php-fpm"]
